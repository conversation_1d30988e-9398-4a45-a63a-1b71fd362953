/**
 * Export tất cả search filter hooks
 * Sử dụng file này để import hooks một cách organized
 */

// Main hook - recommended for most use cases
export { useSearchFilter } from './useSearchFilter';

// Core hooks
export { useFilterState } from './useFilterState';

// Specialized hooks - có thể sử dụng riêng lẻ nếu cần
export { useLocationData } from './useLocationData';
export { usePriceRange } from './usePriceRange';
export { usePropertySize } from './usePropertySize';
export { usePropertyTypes } from './usePropertyTypes';
export { useTransactionType } from './useTransactionType';
export { useRoomFilters } from './useRoomFilters';

// Types (nếu cần export)
export type { TransactionType } from '@/lib/api/services/fetchProperty';

/**
 * Usage examples:
 * 
 * // Recommended - sử dụng main hook
 * import { useSearchFilter } from '@/app/(user)/properties/hooks';
 * 
 * // Hoặc sử dụng hooks riêng lẻ
 * import { usePropertyTypes, useTransactionType } from '@/app/(user)/properties/hooks';
 * 
 * // Import specific hook
 * import { usePriceRange } from '@/app/(user)/properties/hooks/usePriceRange';
 */
