# Search Filter Hooks

<PERSON><PERSON> thống hooks đ<PERSON><PERSON><PERSON> tái cấu trúc để quản lý search filter logic một cách modular và maintainable.

## Cấu trúc Hooks

### 1. useFilterState
Hook cơ bản quản lý filter state và dropdown states.

**Chức năng:**
- Quản lý filter values
- Quản lý dropdown states
- URL sync cơ bản
- Clear filters

### 2. useLocationData
Hook quản lý dữ liệu địa điểm (tỉnh/huyện/xã).

**Chức năng:**
- Fetch provinces/districts/wards từ API
- Quản lý selected location states
- Location change handlers
- Central cities configuration

### 3. usePriceRange
Hook quản lý price range slider và formatting.

**Chức năng:**
- Price slider management
- Price input handling với validation
- Format price labels (bán/thuê)
- Quick price ranges
- URL sync cho min/max price

### 4. usePropertySize
Hook quản lý property size slider và formatting.

**Chứ<PERSON> năng:**
- Size slider management
- Size input handling với validation
- Format size labels
- Quick size ranges
- URL sync cho min/max area

### 5. usePropertyTypes
Hook quản lý property types selection.

**Chức năng:**
- Multi-select property types
- Select all/deselect all
- Apply/reset logic
- Display text formatting

### 6. useTransactionType
Hook quản lý transaction type selection.

**Chức năng:**
- Transaction type selection (bán/thuê/tất cả)
- Apply/reset logic
- Display text formatting
- Context cho price range

### 7. useRoomFilters
Hook quản lý bed/bath count filters.

**Chức năng:**
- Bed count selection với exact match option
- Bath count selection
- Apply/reset logic
- Display text formatting

### 8. useSearchFilter (Main Hook)
Hook tổng hợp kết hợp tất cả hooks con.

**Chức năng:**
- Tích hợp tất cả hooks con
- URL sync toàn diện
- Search logic
- Clear filters toàn bộ

## Cách sử dụng

### Sử dụng hook tổng hợp (Recommended)
```typescript
import { useSearchFilter } from './hooks/useSearchFilter';

function SearchFilterComponent() {
  const {
    // State
    filters,
    searchQuery,
    isSearching,
    
    // Specialized hooks
    locationData,
    priceRange,
    propertySize,
    propertyTypes,
    transactionType,
    roomFilters,
    
    // Actions
    handleSearch,
    clearFilters,
    countActiveFilters,
    setSearchQuery,
  } = useSearchFilter();

  // Component logic...
}
```

### Sử dụng hooks riêng lẻ
```typescript
import { usePropertyTypes } from './hooks/usePropertyTypes';
import { useTransactionType } from './hooks/useTransactionType';

function PropertyTypeFilter() {
  const {
    selectedPropertyTypes,
    propertyTypes,
    handlePropertyTypeChange,
    handleApplyPropertyTypes,
    getPropertyTypeDisplayText,
  } = usePropertyTypes();

  // Component logic...
}
```

## Lợi ích của cấu trúc mới

1. **Separation of Concerns**: Mỗi hook chỉ quản lý một phần logic cụ thể
2. **Reusability**: Có thể sử dụng hooks riêng lẻ ở các component khác
3. **Maintainability**: Dễ dàng maintain và debug từng phần
4. **Type Safety**: TypeScript support tốt hơn
5. **Testing**: Dễ dàng test từng hook riêng biệt
6. **Performance**: Tối ưu re-renders với proper memoization

## Migration từ SearchFilter.tsx

Tất cả logic hiện tại trong SearchFilter.tsx đã được preserve và tái cấu trúc thành hooks:

- ✅ URL sync logic
- ✅ Filter state management
- ✅ Location API integration
- ✅ Price/size range handling
- ✅ Property types multi-select
- ✅ Transaction type logic
- ✅ Room filters với exact match
- ✅ Search và clear functionality
- ✅ Dropdown states management

## Tương lai

Hooks này chuẩn bị sẵn sàng cho việc refactor SearchFilter.tsx component để sử dụng useSearchFilter hook thay vì quản lý state trực tiếp trong component.
