/**
 * Example usage c<PERSON><PERSON> các hooks mới
 * File này chỉ để demo, không được sử dụng trong production
 */

import React from 'react';
import { useSearchFilter } from './useSearchFilter';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

export function SearchFilterExample() {
  const {
    // State
    filters,
    searchQuery,
    isSearching,
    
    // Specialized hooks
    locationData,
    priceRange,
    propertySize,
    propertyTypes,
    transactionType,
    roomFilters,
    
    // Actions
    handleSearch,
    clearFilters,
    countActiveFilters,
    setSearchQuery,
  } = useSearchFilter();

  return (
    <div className="p-4 space-y-4">
      <h2 className="text-xl font-bold">Search Filter Hooks Example</h2>
      
      {/* Search Input */}
      <div>
        <label className="block text-sm font-medium mb-1">Search Query</label>
        <Input
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="Tìm kiếm bất động sản..."
        />
      </div>

      {/* Location */}
      <div>
        <label className="block text-sm font-medium mb-1">Location</label>
        <p className="text-sm text-gray-600">
          Selected: {locationData.getLocationDisplayText()}
        </p>
        <p className="text-xs text-gray-500">
          Provinces: {locationData.provinces.length}, 
          Districts: {locationData.districts.length}, 
          Wards: {locationData.wards.length}
        </p>
      </div>

      {/* Property Types */}
      <div>
        <label className="block text-sm font-medium mb-1">Property Types</label>
        <p className="text-sm text-gray-600">
          Display: {propertyTypes.getPropertyTypeDisplayText()}
        </p>
        <p className="text-xs text-gray-500">
          Selected: {propertyTypes.selectedPropertyTypes.join(', ') || 'None'}
        </p>
        <div className="flex gap-2 mt-2">
          {propertyTypes.propertyTypes.map(type => (
            <Button
              key={type.id}
              size="sm"
              variant={propertyTypes.selectedPropertyTypes.includes(type.id) ? "default" : "outline"}
              onClick={() => propertyTypes.handlePropertyTypeChange(type.id)}
            >
              {type.label}
            </Button>
          ))}
        </div>
      </div>

      {/* Transaction Type */}
      <div>
        <label className="block text-sm font-medium mb-1">Transaction Type</label>
        <p className="text-sm text-gray-600">
          Display: {transactionType.getTransactionTypeDisplayText()}
        </p>
        <p className="text-xs text-gray-500">
          Selected: {transactionType.selectedTransactionType || 'None'}
        </p>
        <div className="flex gap-2 mt-2">
          {transactionType.transactionTypes.map(type => (
            <Button
              key={type.id}
              size="sm"
              variant={transactionType.selectedTransactionType === type.id ? "default" : "outline"}
              onClick={() => transactionType.handleTransactionTypeChange(type.id)}
            >
              {type.label}
            </Button>
          ))}
        </div>
      </div>

      {/* Price Range */}
      <div>
        <label className="block text-sm font-medium mb-1">Price Range</label>
        <p className="text-sm text-gray-600">
          Range: {priceRange.formatPriceLabel(priceRange.sliderValue[0])} - {priceRange.formatPriceLabel(priceRange.sliderValue[1], true)}
        </p>
        <p className="text-xs text-gray-500">
          Values: [{priceRange.sliderValue[0]}, {priceRange.sliderValue[1]}]
        </p>
      </div>

      {/* Property Size */}
      <div>
        <label className="block text-sm font-medium mb-1">Property Size</label>
        <p className="text-sm text-gray-600">
          Range: {propertySize.formatSizeLabel(propertySize.sizeSliderValue[0])} - {propertySize.formatSizeLabel(propertySize.sizeSliderValue[1], true)}
        </p>
        <p className="text-xs text-gray-500">
          Values: [{propertySize.sizeSliderValue[0]}, {propertySize.sizeSliderValue[1]}]
        </p>
      </div>

      {/* Room Filters */}
      <div>
        <label className="block text-sm font-medium mb-1">Room Filters</label>
        <p className="text-sm text-gray-600">
          Display: {roomFilters.getRoomDisplayText(filters.bedCountDisplay, filters.bathCountDisplay)}
        </p>
        <p className="text-xs text-gray-500">
          Bed: {roomFilters.selectedBedCount}, 
          Bath: {roomFilters.selectedBathCount}, 
          Exact: {roomFilters.isExactBedMatch ? 'Yes' : 'No'}
        </p>
      </div>

      {/* Active Filters Count */}
      <div>
        <p className="text-sm font-medium">
          Active Filters: {countActiveFilters()}
        </p>
      </div>

      {/* Actions */}
      <div className="flex gap-2">
        <Button 
          onClick={handleSearch}
          disabled={isSearching}
          className="bg-red-600 hover:bg-red-700"
        >
          {isSearching ? 'Searching...' : 'Search'}
        </Button>
        
        <Button 
          onClick={clearFilters}
          variant="outline"
          className="text-red-600 hover:text-red-700"
        >
          Clear Filters
        </Button>
      </div>

      {/* Debug Info */}
      <details className="mt-4">
        <summary className="cursor-pointer text-sm font-medium">Debug Info</summary>
        <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
          {JSON.stringify({
            filters,
            searchQuery,
            isSearching,
            locationSelected: {
              province: locationData.selectedProvince,
              district: locationData.selectedDistrict,
              ward: locationData.selectedWard,
            },
            priceRange: priceRange.sliderValue,
            propertySize: propertySize.sizeSliderValue,
            propertyTypes: propertyTypes.selectedPropertyTypes,
            transactionType: transactionType.selectedTransactionType,
            roomFilters: {
              bed: roomFilters.selectedBedCount,
              bath: roomFilters.selectedBathCount,
              exact: roomFilters.isExactBedMatch,
            }
          }, null, 2)}
        </pre>
      </details>
    </div>
  );
}

export default SearchFilterExample;
