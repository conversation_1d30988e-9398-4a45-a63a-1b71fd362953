import { useState, useCallback, useMemo } from 'react';

interface PropertySizeState {
  sizeSliderValue: [number, number];
  minSize: string;
  maxSize: string;
  setSizeSliderValue: (value: [number, number]) => void;
  setMinSize: (size: string) => void;
  setMaxSize: (size: string) => void;
  resetPropertySize: () => void;
  formatSizeLabel: (value: number, isMax?: boolean) => string;
  formatSizeDisplay: (sizeRange: string) => string;
  getSizeRangeString: () => string;
  sizeRanges: Array<{
    label: string;
    value: [number, number];
  }>;
  sizeDistribution: Array<{
    size: string;
    count: number;
  }>;
}

/**
 * Hook quản lý logic property size slider và formatting
 * Tương tự usePriceRange nhưng cho diện tích bất động sản
 */
export function usePropertySize(): PropertySizeState {
  const [minSize, setMinSize] = useState('');
  const [maxSize, setMaxSize] = useState('');
  const [sizeSliderValue, setSizeSliderValue] = useState<[number, number]>([0, 1000]);

  // Predefined size ranges - synced with SearchFilter.tsx
  const sizeRanges = useMemo(
    () => [
      { label: 'Dưới 30m²', value: [0, 30] as [number, number] },
      { label: '30-50m²', value: [30, 50] as [number, number] },
      { label: '50-70m²', value: [50, 70] as [number, number] },
      { label: '70-100m²', value: [70, 100] as [number, number] },
      { label: '100-150m²', value: [100, 150] as [number, number] },
      { label: 'Trên 150m²', value: [150, 1000] as [number, number] },
    ],
    []
  );

  // Size distribution data - synced with SearchFilter.tsx
  const sizeDistribution = useMemo(
    () => [
      { size: '0-30', count: 120 },
      { size: '30-50', count: 280 },
      { size: '50-70', count: 320 },
      { size: '70-100', count: 250 },
      { size: '100-150', count: 180 },
      { size: '150-200', count: 120 },
      { size: '200-300', count: 80 },
      { size: '300-500', count: 50 },
      { size: '500-1000', count: 30 },
      { size: '1000+', count: 20 },
    ],
    []
  );

  // Format size label for slider
  const formatSizeLabel = useCallback((value: number, isMax: boolean = false) => {
    if (value >= 1000) {
      return `${(value / 1000).toFixed(1)} nghìn${isMax ? '+' : ''} m²`;
    }
    return `${value}${isMax ? '+' : ''} m²`;
  }, []);

  // Format size display for button
  const formatSizeDisplay = useCallback((sizeRange: string) => {
    if (!sizeRange) return 'Diện tích';
    return sizeRange;
  }, []);

  // Get size range string for filters
  const getSizeRangeString = useCallback(() => {
    if (sizeSliderValue[0] === 0 && sizeSliderValue[1] === 1000) {
      return '';
    }
    return `${formatSizeLabel(sizeSliderValue[0])} đến ${formatSizeLabel(sizeSliderValue[1])}`;
  }, [sizeSliderValue, formatSizeLabel]);

  // Reset size range
  const resetPropertySize = useCallback(() => {
    setSizeSliderValue([0, 1000]);
    setMinSize('');
    setMaxSize('');
  }, []);

  return {
    sizeSliderValue,
    minSize,
    maxSize,
    setSizeSliderValue,
    setMinSize,
    setMaxSize,
    resetPropertySize,
    formatSizeLabel,
    formatSizeDisplay,
    getSizeRangeString,
    sizeRanges,
    sizeDistribution,
  };
}
