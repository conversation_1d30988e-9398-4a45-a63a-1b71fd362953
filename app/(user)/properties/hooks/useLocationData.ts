import { useState, useEffect } from 'react';

// Define interfaces for location data
interface Province {
  code: number;
  name: string;
  districts?: District[];
}

interface District {
  code: number;
  name: string;
  wards?: Ward[];
}

interface Ward {
  code: number;
  name: string;
}

interface LocationData {
  provinces: Province[];
  districts: District[];
  wards: Ward[];
  selectedProvince: string;
  selectedDistrict: string;
  selectedWard: string;
  loading: boolean;
  setSelectedProvince: (code: string) => void;
  setSelectedDistrict: (code: string) => void;
  setSelectedWard: (code: string) => void;
  resetLocation: () => void;
  handleLocationChange: (type: 'province' | 'district' | 'ward', value: string, onFilterChange: (category: string, value: string) => void) => void;
}

interface LocationFilters {
  city?: string;
  district?: string;
  ward?: string;
}

/**
 * Hook quản lý dữ liệu và trạng thái location (tỉnh/huyện/xã)
 * Sử dụng API trực tiếp từ provinces.open-api.vn
 */
export function useLocationData(initialFilters?: LocationFilters): LocationData {
  const [selectedProvince, setSelectedProvince] = useState<string>('');
  const [selectedDistrict, setSelectedDistrict] = useState<string>('');
  const [selectedWard, setSelectedWard] = useState<string>('');
  const [provinces, setProvinces] = useState<Province[]>([]);
  const [districts, setDistricts] = useState<District[]>([]);
  const [wards, setWards] = useState<Ward[]>([]);
  const [loading, setLoading] = useState(false);

  // Fetch provinces on component mount
  useEffect(() => {
    const fetchProvinces = async () => {
      setLoading(true);
      try {
        const response = await fetch('https://provinces.open-api.vn/api/p/');
        if (!response.ok) throw new Error('Failed to fetch provinces');
        const data = await response.json();
        setProvinces(data);
      } catch (error) {
        console.error('Error fetching provinces:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProvinces();
  }, []);

  // Fetch districts when province changes
  useEffect(() => {
    if (!selectedProvince) {
      setDistricts([]);
      return;
    }

    const fetchDistricts = async () => {
      setLoading(true);
      try {
        const response = await fetch(
          `https://provinces.open-api.vn/api/p/${selectedProvince}?depth=2`
        );
        if (!response.ok) throw new Error('Failed to fetch districts');
        const data = await response.json();
        setDistricts(data.districts || []);
      } catch (error) {
        console.error('Error fetching districts:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDistricts();
  }, [selectedProvince]);

  // Fetch wards when district changes
  useEffect(() => {
    if (!selectedDistrict) {
      setWards([]);
      return;
    }

    const fetchWards = async () => {
      setLoading(true);
      try {
        const response = await fetch(
          `https://provinces.open-api.vn/api/d/${selectedDistrict}?depth=2`
        );
        if (!response.ok) throw new Error('Failed to fetch wards');
        const data = await response.json();
        setWards(data.wards || []);
      } catch (error) {
        console.error('Error fetching wards:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchWards();
  }, [selectedDistrict]);

  // Initialize from filters if provided
  useEffect(() => {
    if (initialFilters?.city && provinces.length > 0) {
      const province = provinces.find(p => p.name === initialFilters.city);
      if (province) {
        setSelectedProvince(province.code.toString());
      }
    }
  }, [initialFilters?.city, provinces]);

  // Set selected district when districts are loaded
  useEffect(() => {
    if (initialFilters?.district && districts.length > 0) {
      const district = districts.find(d => d.name === initialFilters.district);
      if (district) {
        setSelectedDistrict(district.code.toString());
      }
    }
  }, [initialFilters?.district, districts]);

  // Set selected ward when wards are loaded
  useEffect(() => {
    if (initialFilters?.ward && wards.length > 0) {
      const ward = wards.find(w => w.name === initialFilters.ward);
      if (ward) {
        setSelectedWard(ward.code.toString());
      }
    }
  }, [initialFilters?.ward, wards]);

  // Reset dependent selections when parent changes
  const handleProvinceChange = (code: string) => {
    setSelectedProvince(code);
    setSelectedDistrict('');
    setSelectedWard('');
  };

  const handleDistrictChange = (code: string) => {
    setSelectedDistrict(code);
    setSelectedWard('');
  };

  const resetLocation = () => {
    setSelectedProvince('');
    setSelectedDistrict('');
    setSelectedWard('');
  };

  // Handle location change and update filters
  const handleLocationChange = (
    type: 'province' | 'district' | 'ward',
    value: string,
    onFilterChange: (category: string, value: string) => void
  ) => {
    if (type === 'province') {
      const province = provinces.find(p => p.code.toString() === value);
      setSelectedProvince(value);
      setSelectedDistrict('');
      setSelectedWard('');
      if (province?.name) {
        onFilterChange('city', province.name);
        onFilterChange('district', '');
        onFilterChange('ward', '');
      } else {
        onFilterChange('city', '');
      }
    } else if (type === 'district') {
      const district = districts.find(d => d.code.toString() === value);
      setSelectedDistrict(value);
      setSelectedWard('');
      if (district?.name) {
        onFilterChange('district', district.name);
        onFilterChange('ward', '');
      } else {
        onFilterChange('district', '');
      }
    } else {
      const ward = wards.find(w => w.code.toString() === value);
      setSelectedWard(value);
      if (ward?.name) {
        onFilterChange('ward', ward.name);
      } else {
        onFilterChange('ward', '');
      }
    }
  };

  return {
    provinces,
    districts,
    wards,
    selectedProvince,
    selectedDistrict,
    selectedWard,
    loading,
    setSelectedProvince: handleProvinceChange,
    setSelectedDistrict: handleDistrictChange,
    setSelectedWard,
    resetLocation,
    handleLocationChange,
  };
}
