import { useState, useEffect } from 'react';

// Define interfaces for location data
interface Province {
  code: number;
  name: string;
  districts?: District[];
}

interface District {
  code: number;
  name: string;
  wards?: Ward[];
}

interface Ward {
  code: number;
  name: string;
}

interface LocationData {
  provinces: Province[];
  districts: District[];
  wards: Ward[];
  selectedProvince: string;
  selectedDistrict: string;
  selectedWard: string;
  loading: boolean;
  setSelectedProvince: (code: string) => void;
  setSelectedDistrict: (code: string) => void;
  setSelectedWard: (code: string) => void;
  resetLocation: () => void;
  handleLocationChange: (type: 'province' | 'district' | 'ward', value: string, onFilterChange: (category: string, value: string) => void) => void;
  getLocationDisplayText: () => string;
  getCentralCities: () => Array<{
    code: string;
    name: string;
    image: string;
    icon: string;
  }>;
}

interface LocationFilters {
  city?: string;
  district?: string;
  ward?: string;
}

// Central cities configuration - synced with SearchFilter.tsx
const CENTRAL_CITIES = [
  {
    code: '1',
    name: '<PERSON><PERSON>',
    image: 'https://www.atlys.com/_next/image?url=https%3A%2F%2Fimagedelivery.net%2FW3Iz4WACAy2J0qT0cCT3xA%2Fdidi%2Farticles%2Frlopvfixv5sap0yupj9osxaa%2Fpublic&w=1920&q=75',
    icon: '🏛️',
  },
  {
    code: '79',
    name: 'TP. Hồ Chí Minh',
    image: 'https://media.vneconomy.vn/images/upload/2024/05/20/tphcm-16726501373541473396704-16994302498261147920222.jpg',
    icon: '🌆',
  },
  {
    code: '48',
    name: 'Đà Nẵng',
    image: 'https://vcdn1-dulich.vnecdn.net/2022/06/03/cauvang-1654247842-9403-1654247849.jpg?w=1200&h=0&q=100&dpr=1&fit=crop&s=Swd6JjpStebEzT6WARcoOA',
    icon: '🏖️',
  },
  {
    code: '92',
    name: 'Cần Thơ',
    image: 'https://ik.imagekit.io/tvlk/blog/2021/11/dia-diem-du-lich-can-tho-cover.jpg',
    icon: '🌾',
  },
  {
    code: '31',
    name: 'Hải Phòng',
    image: 'https://heza.gov.vn/wp-content/uploads/2023/09/hai_phong-scaled.jpg',
    icon: '⚓',
  },
];

/**
 * Hook quản lý dữ liệu và trạng thái location (tỉnh/huyện/xã)
 * Sử dụng API trực tiếp từ provinces.open-api.vn với tích hợp filter system
 */
export function useLocationData(initialFilters?: LocationFilters): LocationData {
  const [selectedProvince, setSelectedProvince] = useState<string>('');
  const [selectedDistrict, setSelectedDistrict] = useState<string>('');
  const [selectedWard, setSelectedWard] = useState<string>('');
  const [provinces, setProvinces] = useState<Province[]>([]);
  const [districts, setDistricts] = useState<District[]>([]);
  const [wards, setWards] = useState<Ward[]>([]);
  const [loading, setLoading] = useState(false);

  // Fetch provinces on component mount
  useEffect(() => {
    const fetchProvinces = async () => {
      setLoading(true);
      try {
        const response = await fetch('https://provinces.open-api.vn/api/p/');
        if (!response.ok) throw new Error('Failed to fetch provinces');
        const data = await response.json();
        setProvinces(data);
      } catch (error) {
        console.error('Error fetching provinces:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProvinces();
  }, []);

  // Fetch districts when province changes
  useEffect(() => {
    if (!selectedProvince) {
      setDistricts([]);
      return;
    }

    const fetchDistricts = async () => {
      setLoading(true);
      try {
        const response = await fetch(
          `https://provinces.open-api.vn/api/p/${selectedProvince}?depth=2`
        );
        if (!response.ok) throw new Error('Failed to fetch districts');
        const data = await response.json();
        setDistricts(data.districts || []);
      } catch (error) {
        console.error('Error fetching districts:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDistricts();
  }, [selectedProvince]);

  // Fetch wards when district changes
  useEffect(() => {
    if (!selectedDistrict) {
      setWards([]);
      return;
    }

    const fetchWards = async () => {
      setLoading(true);
      try {
        const response = await fetch(
          `https://provinces.open-api.vn/api/d/${selectedDistrict}?depth=2`
        );
        if (!response.ok) throw new Error('Failed to fetch wards');
        const data = await response.json();
        setWards(data.wards || []);
      } catch (error) {
        console.error('Error fetching wards:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchWards();
  }, [selectedDistrict]);

  // Initialize from filters if provided
  useEffect(() => {
    if (initialFilters?.city && provinces.length > 0) {
      const province = provinces.find(p => p.name === initialFilters.city);
      if (province) {
        setSelectedProvince(province.code.toString());
      }
    }
  }, [initialFilters?.city, provinces]);

  // Set selected district when districts are loaded
  useEffect(() => {
    if (initialFilters?.district && districts.length > 0) {
      const district = districts.find(d => d.name === initialFilters.district);
      if (district) {
        setSelectedDistrict(district.code.toString());
      }
    }
  }, [initialFilters?.district, districts]);

  // Set selected ward when wards are loaded
  useEffect(() => {
    if (initialFilters?.ward && wards.length > 0) {
      const ward = wards.find(w => w.name === initialFilters.ward);
      if (ward) {
        setSelectedWard(ward.code.toString());
      }
    }
  }, [initialFilters?.ward, wards]);

  // Reset dependent selections when parent changes
  const handleProvinceChange = (code: string) => {
    setSelectedProvince(code);
    setSelectedDistrict('');
    setSelectedWard('');
  };

  const handleDistrictChange = (code: string) => {
    setSelectedDistrict(code);
    setSelectedWard('');
  };

  const resetLocation = () => {
    setSelectedProvince('');
    setSelectedDistrict('');
    setSelectedWard('');
  };

  // Handle location change and update filters
  const handleLocationChange = (
    type: 'province' | 'district' | 'ward',
    value: string,
    onFilterChange: (category: string, value: string) => void
  ) => {
    if (type === 'province') {
      const province = provinces.find(p => p.code.toString() === value);
      setSelectedProvince(value);
      setSelectedDistrict('');
      setSelectedWard('');
      if (province?.name) {
        onFilterChange('city', province.name);
        onFilterChange('district', '');
        onFilterChange('ward', '');
      } else {
        onFilterChange('city', '');
      }
    } else if (type === 'district') {
      const district = districts.find(d => d.code.toString() === value);
      setSelectedDistrict(value);
      setSelectedWard('');
      if (district?.name) {
        onFilterChange('district', district.name);
        onFilterChange('ward', '');
      } else {
        onFilterChange('district', '');
      }
    } else {
      const ward = wards.find(w => w.code.toString() === value);
      setSelectedWard(value);
      if (ward?.name) {
        onFilterChange('ward', ward.name);
      } else {
        onFilterChange('ward', '');
      }
    }
  };

  // Get display text for location button
  const getLocationDisplayText = useCallback(() => {
    if (selectedProvince) {
      const province = provinces.find(p => p.code.toString() === selectedProvince);
      return province?.name || 'Chọn địa điểm';
    }
    return 'Chọn địa điểm';
  }, [selectedProvince, provinces]);

  // Get central cities configuration
  const getCentralCities = useCallback(() => {
    return CENTRAL_CITIES;
  }, []);

  // Sync from filter values
  const syncFromFilterValues = useCallback((filters: LocationFilters) => {
    // Find and set province code based on name
    if (filters.city && provinces.length > 0) {
      const province = provinces.find(p => p.name === filters.city);
      if (province) {
        setSelectedProvince(province.code.toString());
      }
    } else {
      setSelectedProvince('');
    }

    // District and ward will be set via useEffect when data is loaded
  }, [provinces]);

  return {
    provinces,
    districts,
    wards,
    selectedProvince,
    selectedDistrict,
    selectedWard,
    loading,
    setSelectedProvince: handleProvinceChange,
    setSelectedDistrict: handleDistrictChange,
    setSelectedWard,
    resetLocation,
    handleLocationChange,
    getLocationDisplayText,
    getCentralCities,
    // Additional utility method
    syncFromFilterValues,
  } as LocationData & {
    syncFromFilterValues: (filters: LocationFilters) => void;
  };
}
