import { useState, useCallback, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { TransactionType } from '@/lib/api/services/fetchProperty';

interface FilterValues {
  city?: string;
  district?: string;
  ward?: string;
  propertyType?: string;
  transactionType?: TransactionType;
  priceRange?: string;
  propertySize?: string;
  buildYear?: string;
  bedCount?: string;
  bathCount?: string;
  exactBedMatch?: string;
  bedCountDisplay?: string;
  bathCountDisplay?: string;
  [key: string]: string | undefined;
}

interface DropdownStates {
  isPropertyTypeOpen: boolean;
  isTransactionTypeOpen: boolean;
  isPriceRangeOpen: boolean;
  isPropertySizeOpen: boolean;
  isRoomsDropdownOpen: boolean;
  isMoreFiltersOpen: boolean;
  [key: string]: boolean;
}

interface FilterState {
  filters: FilterValues;
  dropdownStates: DropdownStates;
  selectedPropertyTypes: string[];
  selectedTransactionType: string;
  selectedBedCount: string;
  selectedBathCount: string;
  isExactBedMatch: boolean;
  searchQuery: string;
  isSearching: boolean;
  updateFilter: (key: string, value: string) => void;
  updateFilters: (updates: Partial<FilterValues>) => void;
  clearFilters: () => void;
  toggleDropdown: (dropdown: keyof DropdownStates) => void;
  setSelectedPropertyTypes: (types: string[]) => void;
  setSelectedTransactionType: (type: string) => void;
  setSelectedBedCount: (count: string) => void;
  setSelectedBathCount: (count: string) => void;
  setIsExactBedMatch: (exact: boolean) => void;
  setSearchQuery: (query: string) => void;
  setIsSearching: (searching: boolean) => void;
  countActiveFilters: () => number;
  handleSearch: () => void;
  syncFromURL: () => void;
}

/**
 * Hook quản lý trạng thái filters và dropdown states
 * Tập trung hóa logic filter state management với tích hợp hooks mới
 */
export function useFilterState(initialFilters?: FilterValues): FilterState {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [filters, setFilters] = useState<FilterValues>(initialFilters || {});
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);

  const [dropdownStates, setDropdownStates] = useState<DropdownStates>({
    isPropertyTypeOpen: false,
    isTransactionTypeOpen: false,
    isPriceRangeOpen: false,
    isPropertySizeOpen: false,
    isRoomsDropdownOpen: false,
    isMoreFiltersOpen: false,
  });

  // Selection states
  const [selectedPropertyTypes, setSelectedPropertyTypes] = useState<string[]>([]);
  const [selectedTransactionType, setSelectedTransactionType] = useState<string>('');
  const [selectedBedCount, setSelectedBedCount] = useState<string>('any');
  const [selectedBathCount, setSelectedBathCount] = useState<string>('any');
  const [isExactBedMatch, setIsExactBedMatch] = useState(false);

  // Update single filter
  const updateFilter = useCallback((key: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  }, []);

  // Update multiple filters
  const updateFilters = useCallback((updates: Partial<FilterValues>) => {
    setFilters(prev => ({
      ...prev,
      ...updates,
    }));
  }, []);

  // Clear all filters
  const clearFilters = useCallback(() => {
    setFilters({});
    setSearchQuery('');
    setSelectedPropertyTypes([]);
    setSelectedTransactionType('');
    setSelectedBedCount('any');
    setSelectedBathCount('any');
    setIsExactBedMatch(false);

    // Reset all dropdown states
    setDropdownStates({
      isPropertyTypeOpen: false,
      isTransactionTypeOpen: false,
      isPriceRangeOpen: false,
      isPropertySizeOpen: false,
      isRoomsDropdownOpen: false,
      isMoreFiltersOpen: false,
    });

    // Clear URL parameters by navigating to the base URL
    router.push('/properties');
  }, [router]);

  // Toggle dropdown state
  const toggleDropdown = useCallback((dropdown: keyof DropdownStates) => {
    setDropdownStates(prev => ({
      ...prev,
      [dropdown]: !prev[dropdown],
    }));
  }, []);

  // Set specific dropdown state
  const setDropdownState = useCallback((dropdown: keyof DropdownStates, open: boolean) => {
    setDropdownStates(prev => ({
      ...prev,
      [dropdown]: open,
    }));
  }, []);

  // Close all dropdowns
  const closeAllDropdowns = useCallback(() => {
    setDropdownStates({
      isPropertyTypeOpen: false,
      isTransactionTypeOpen: false,
      isPriceRangeOpen: false,
      isPropertySizeOpen: false,
      isRoomsDropdownOpen: false,
      isMoreFiltersOpen: false,
    });
  }, []);

  // Count active filters
  const countActiveFilters = useCallback(() => {
    return Object.keys(filters).filter(key => filters[key as keyof FilterValues]).length;
  }, [filters]);

  // Handle search and URL update
  const handleSearch = useCallback(() => {
    setIsSearching(true);

    // Build the URL query string from the filters
    const queryParams = new URLSearchParams();

    // Add search term if not empty
    if (searchQuery.trim()) {
      queryParams.set('searchTerm', searchQuery.trim());
    }

    // Add all active filters to URL
    Object.entries(filters).forEach(([key, value]) => {
      if (value && ![
        'priceRange',
        'propertySize',
        'bedCountDisplay',
        'bathCountDisplay',
      ].includes(key)) {
        queryParams.set(key, value);
      }
    });

    // Navigate to the same page with filters in URL
    router.push(`/properties?${queryParams.toString()}`);

    // Reset searching state after a short delay
    setTimeout(() => {
      setIsSearching(false);
    }, 500);
  }, [filters, searchQuery, router]);

  // Sync filters from URL parameters
  const syncFromURL = useCallback(() => {
    const params = new URLSearchParams(searchParams);
    
    // Sync search query
    if (params.has('searchTerm')) {
      setSearchQuery(params.get('searchTerm') || '');
    }

    // Sync filters
    const initialFilters: FilterValues = {};
    params.forEach((value, key) => {
      if (key !== 'searchTerm') {
        initialFilters[key] = value;
      }
    });

    setFilters(initialFilters);
  }, [searchParams]);

  // Sync from URL on mount and when searchParams change
  useEffect(() => {
    syncFromURL();
  }, [syncFromURL]);

  return {
    filters,
    dropdownStates,
    selectedPropertyTypes,
    selectedTransactionType,
    selectedBedCount,
    selectedBathCount,
    isExactBedMatch,
    searchQuery,
    isSearching,
    updateFilter,
    updateFilters,
    clearFilters,
    toggleDropdown,
    setSelectedPropertyTypes,
    setSelectedTransactionType,
    setSelectedBedCount,
    setSelectedBathCount,
    setIsExactBedMatch,
    setSearchQuery,
    setIsSearching,
    countActiveFilters,
    handleSearch,
    syncFromURL,
    setDropdownState,
    closeAllDropdowns,
  } as FilterState & {
    setDropdownState: (dropdown: keyof DropdownStates, open: boolean) => void;
    closeAllDropdowns: () => void;
  };
}
