import { useState, useCallback, useMemo } from 'react';
import { Building2, Home, LandPlot, Store } from 'lucide-react';

interface PropertyType {
  id: string;
  label: string;
  icon: any;
}

interface PropertyTypesState {
  selectedPropertyTypes: string[];
  isPropertyTypeOpen: boolean;
  propertyTypes: PropertyType[];
  setSelectedPropertyTypes: (types: string[]) => void;
  setIsPropertyTypeOpen: (open: boolean) => void;
  handlePropertyTypeChange: (type: string) => void;
  handleSelectAllPropertyTypes: () => void;
  handleApplyPropertyTypes: (onFilterChange: (key: string, value: string) => void) => void;
  resetPropertyTypes: () => void;
  getPropertyTypeDisplayText: () => string;
  isAllSelected: boolean;
}

/**
 * Hook quản lý logic property types selection
 * Hỗ trợ multi-select, select all, và apply/reset functionality
 */
export function usePropertyTypes(): PropertyTypesState {
  const [selectedPropertyTypes, setSelectedPropertyTypes] = useState<string[]>([]);
  const [isPropertyTypeOpen, setIsPropertyTypeOpen] = useState(false);

  // Property types configuration - synced with SearchFilter.tsx
  const propertyTypes = useMemo((): PropertyType[] => [
    { id: 'apartment', label: 'Chung cư', icon: Building2 },
    { id: 'villa', label: 'Biệt thự', icon: Home },
    { id: 'land_plot', label: 'Đất', icon: LandPlot },
    { id: 'shop_house', label: 'Nhà phố', icon: Store },
  ], []);

  // Check if all property types are selected
  const isAllSelected = useMemo(() => {
    return selectedPropertyTypes.length === propertyTypes.length;
  }, [selectedPropertyTypes.length, propertyTypes.length]);

  // Handle individual property type selection
  const handlePropertyTypeChange = useCallback((type: string) => {
    setSelectedPropertyTypes(prev => {
      if (prev.includes(type)) {
        return prev.filter(t => t !== type);
      }
      return [...prev, type];
    });
  }, []);

  // Handle select all/deselect all
  const handleSelectAllPropertyTypes = useCallback(() => {
    if (isAllSelected) {
      setSelectedPropertyTypes([]);
    } else {
      setSelectedPropertyTypes(propertyTypes.map(type => type.id));
    }
  }, [isAllSelected, propertyTypes]);

  // Apply property types to filters
  const handleApplyPropertyTypes = useCallback((onFilterChange: (key: string, value: string) => void) => {
    if (selectedPropertyTypes.length === 0) {
      onFilterChange('propertyType', '');
    } else if (selectedPropertyTypes.length === 1) {
      onFilterChange('propertyType', selectedPropertyTypes[0]);
    } else {
      onFilterChange('propertyType', selectedPropertyTypes.join(','));
    }
    setIsPropertyTypeOpen(false);
  }, [selectedPropertyTypes]);

  // Reset property types selection
  const resetPropertyTypes = useCallback(() => {
    setSelectedPropertyTypes([]);
    setIsPropertyTypeOpen(false);
  }, []);

  // Get display text for property type button
  const getPropertyTypeDisplayText = useCallback(() => {
    if (selectedPropertyTypes.length === 0) {
      return 'Loại nhà';
    }
    
    if (selectedPropertyTypes.length > 1) {
      return `${selectedPropertyTypes.length} loại`;
    }

    // Single selection - find the label
    const selectedType = propertyTypes.find(type => type.id === selectedPropertyTypes[0]);
    return selectedType?.label || 'Loại nhà';
  }, [selectedPropertyTypes, propertyTypes]);

  // Sync selected types from filter value
  const syncFromFilterValue = useCallback((filterValue: string) => {
    if (!filterValue) {
      setSelectedPropertyTypes([]);
      return;
    }

    if (filterValue.includes(',')) {
      setSelectedPropertyTypes(filterValue.split(','));
    } else {
      setSelectedPropertyTypes([filterValue]);
    }
  }, []);

  return {
    selectedPropertyTypes,
    isPropertyTypeOpen,
    propertyTypes,
    setSelectedPropertyTypes,
    setIsPropertyTypeOpen,
    handlePropertyTypeChange,
    handleSelectAllPropertyTypes,
    handleApplyPropertyTypes,
    resetPropertyTypes,
    getPropertyTypeDisplayText,
    isAllSelected,
    // Additional utility method for syncing from URL/filter
    syncFromFilterValue,
  } as PropertyTypesState & { syncFromFilterValue: (filterValue: string) => void };
}
