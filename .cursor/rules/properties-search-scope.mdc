---
alwaysApply: true
---

# Properties Search Scope Restriction

## Scope Limitations
- **ONLY work with properties module for users**: `app/(user)/properties/`
- **Focus specifically on search functionality**
- **DO NOT modify files outside of properties scope**
- **DO NOT create unnecessary files**

## Allowed Files/Directories
- `app/(user)/properties/page.tsx`
- `app/(user)/properties/components/SearchFilter.tsx`
- `app/(user)/properties/components/PropertiesClient.tsx`
- `app/(user)/properties/components/PropertyListings.tsx`
- `app/(user)/properties/components/PropertiesMap.tsx`
- Related hooks: `hooks/useProperty.ts`, `hooks/useFavoriteProperty.ts`
- Related API services: `lib/api/services/fetchProperty.ts`, `lib/api/services/fetchFavoriteProperty.ts`

## Restrictions
- **NO modifications to admin properties**: `app/(admin)/admin/property/`
- **NO modifications to saler properties**: `app/(saler)/saler/property/`
- **NO modifications to property detail pages**: `app/(user)/properties/[id]/`
- **NO creation of new files unless specifically requested**
- **NO modifications to unrelated components, hooks, or services**

## Search Functionality Focus
- Work only on search filters, search logic, search UI
- Optimize SearchFilter.tsx component
- Improve search performance and UX
- Focus on search-related state management

## Code Quality Guidelines
- Follow existing TypeScript patterns
- Use existing UI components from `@/components/ui/`
- Maintain responsive design principles
- Keep search functionality clean and performant
